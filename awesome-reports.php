<?php

/**
 * Plugin Name: Awesome Reports
 * Plugin URI: https://awesomereports.com/
 * Description: Display update statistics directly in the WordPress admin or send reports via email.
 * Version: 1.0.0
 * Author: Bohemia Plugins
 * Author URI: https://bohemiaplugins.com/
 * Text Domain: awesome-reports
 * Domain Path: /languages/
 * 
 * Awesome Reports, 
 * Copyright (C) 2025, Bohemia Plugins, <EMAIL>
 * 
 * Awesome Reports is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * any later version.
 *
 * Awesome Reports is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with Awesome Reports. If not, see <https://www.gnu.org/licenses/>.
 * 
 * Awesome Reports incorporates code from WordPress plugin "Awesome Reports"
 * <https://wordpress.org/plugins/awesome-reports/> by <PERSON> <https://mikegillihan.com/>.
 * License: GNU GPL, Version 2
 */

if (! defined('ABSPATH')) {
    exit;
}

define('AWESOME_REPORTS_VERSION', '1.0.0');

/**
 * Create the database table needed for tracking.
 */
function awesome_reports_data_install() {
    global $wpdb;

    $awesome_reports_table_name = $wpdb->prefix . 'awesome_reports';

    $charset_collate = $wpdb->get_charset_collate();

    $awesome_reports_sql = "CREATE TABLE $awesome_reports_table_name (
		id mediumint(9) NOT NULL AUTO_INCREMENT,
		date date DEFAULT '0000-00-00' NOT NULL,
        type varchar(191),
        name varchar(191),
        slug varchar(191),
        version_before varchar(191),
        version_after varchar(191),
        active tinyint(1),
		UNIQUE KEY id (id)
	) $charset_collate;";

    require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
    dbDelta($awesome_reports_sql);

    add_option('awesome_reports_version', AWESOME_REPORTS_VERSION);
}
register_activation_hook(__FILE__, 'awesome_reports_data_install');

/**
 * On plugin activation schedule our daily check for updates.
 */
function awesome_reports_check_for_updates_daily_schedule() {
    // Use wp_next_scheduled to check if the event is already scheduled
    $timestamp = wp_next_scheduled('awesome_reports_check_for_updates_daily');
    // If $timestamp == false schedule daily backups since it hasn't been done previously
    if ($timestamp == false) {
        $timezone = wp_timezone();
        $midnight = new DateTime("00:00:00", $timezone);
        // Schedule the event for right now, then to repeat daily
        wp_schedule_event($midnight->format('U'), 'daily', 'awesome_reports_check_for_updates_daily');
    }
}
register_activation_hook(__FILE__, 'awesome_reports_check_for_updates_daily_schedule');
register_deactivation_hook(__FILE__, 'awesome_reports_check_for_updates_daily_schedule_clear');

/**
 * On plugin deactivation remove the scheduled events.
 */
function awesome_reports_check_for_updates_daily_schedule_clear() {
    wp_clear_scheduled_hook('awesome_reports_check_for_updates_daily');
}
add_action('upgrader_process_complete', 'awesome_reports_after_update', 10, 2);

/**
 * After an update has run, check and log in database.
 */
function awesome_reports_after_update($upgrader_object, $options) {
    if ($options['action'] == 'update') {
        awesome_reports_check_for_updates();
    }
}
add_action('awesome_reports_check_for_updates_daily', 'awesome_reports_check_for_updates');

/**
 * Loop through each type of update and determine if there is now a newer version.
 */
function awesome_reports_check_for_updates() {
    global $wpdb;
    $awesome_reports_table_name = $wpdb->prefix . 'awesome_reports';

    if (!function_exists('get_plugins')) {
        require_once ABSPATH . 'wp-admin/includes/plugin.php';
    }

    $timezone  = wp_timezone();
    $now       = new DateTime("now", $timezone);
    $mysqldate = $now->format('Y-m-d');

    $wordpress_version = get_bloginfo('version');

    $last_wp_update = $wpdb->get_row($wpdb->prepare("SELECT * FROM $awesome_reports_table_name WHERE `type` = 'wp' AND `slug` = %s ORDER BY `date` DESC", array('wp')));

    $today_wp_update = $wpdb->get_row($wpdb->prepare("SELECT * FROM $awesome_reports_table_name WHERE `type` = 'wp' AND slug = %s AND date = %s", array(
        'wp',
        $mysqldate
    )));

    if (!$last_wp_update || version_compare($wordpress_version, $last_wp_update->version_after, '>')) {

        $last_version = null;
        if ($last_wp_update) {
            $last_version = $last_wp_update->version_after;
        }

        $update_id = null;
        if ($today_wp_update) {
            $update_id = $today_wp_update->id;
        }

        $wp_update = array(
            'id'             => $update_id,
            'date'           => $mysqldate,
            'type'           => 'wp',
            'name'           => 'WordPress',
            'slug'           => 'wp',
            'version_before' => $last_version,
            'version_after'  => $wordpress_version,
            'active'         => null,
        );

        awesome_reports_track_update($wp_update);
    }

    $themes = wp_get_themes();

    foreach ($themes as $theme_slug => $theme) {

        $theme_active = false;
        $active_theme = get_option('stylesheet');

        if ($theme_slug == $active_theme) {
            $theme_active = true;
        }

        $last_theme_update = $wpdb->get_row($wpdb->prepare("SELECT * FROM $awesome_reports_table_name WHERE `type` = 'theme' AND `slug` = %s ORDER BY `date` DESC", array($theme_slug)));

        $today_theme_update = $wpdb->get_row($wpdb->prepare("SELECT * FROM $awesome_reports_table_name WHERE `type` = 'theme' AND slug = %s AND date = %s", array(
            $theme_slug,
            $mysqldate
        )));

        if (! $last_theme_update || version_compare($theme->get('Version'), $last_theme_update->version_after, '>')) {

            $last_version = null;
            if ($last_theme_update) {
                $last_version = $last_theme_update->version_after;
            }

            $update_id = null;
            if ($today_theme_update) {
                $update_id = $today_theme_update->id;
            }

            $theme_update = array(
                'id'             => $update_id,
                'date'           => $mysqldate,
                'type'           => 'theme',
                'name'           => $theme['Name'],
                'slug'           => $theme_slug,
                'version_before' => $last_version,
                'version_after'  => $theme['Version'],
                'active'         => $theme_active,
            );

            awesome_reports_track_update($theme_update);
        }
    }

    $plugins = get_plugins();

    foreach ($plugins as $plugin_slug => $plugin) {

        $plugin_active = false;
        if (is_plugin_active($plugin_slug)) {
            $plugin_active = true;
        }

        $last_plugin_update = $wpdb->get_row($wpdb->prepare("SELECT * FROM $awesome_reports_table_name WHERE `type` = 'plugin' AND `slug` = %s ORDER BY `date` DESC", array($plugin_slug)));

        $today_plugin_update = $wpdb->get_row($wpdb->prepare("SELECT * FROM $awesome_reports_table_name WHERE `type` = 'plugin' AND `slug` = %s AND `date` = %s", array(
            $plugin_slug,
            $mysqldate
        )));

        if (! $last_plugin_update || version_compare($plugin['Version'], $last_plugin_update->version_after, '>')) {

            $last_version = null;
            if ($last_plugin_update) {
                $last_version = $last_plugin_update->version_after;
            }

            $update_id = null;
            if ($today_plugin_update) {
                $update_id = $today_plugin_update->id;
            }

            $plugin_update = array(
                'id'             => $update_id,
                'date'           => $mysqldate,
                'type'           => 'plugin',
                'name'           => $plugin['Name'],
                'slug'           => $plugin_slug,
                'version_before' => $last_version,
                'version_after'  => $plugin['Version'],
                'active'         => $plugin_active,
            );

            awesome_reports_track_update($plugin_update);
        }
    }

    do_action('awesome_reports_check');
}

/**
 * Track a single update and add it to the database.
 */
function awesome_reports_track_update($thing_to_track) {

    global $wpdb;
    $awesome_reports_table_name = $wpdb->prefix . 'awesome_reports';

    $new_entry = $wpdb->replace(
        $awesome_reports_table_name,
        $thing_to_track,
        array(
            '%d',
            '%s',
            '%s',
            '%s',
            '%s',
            '%s',
            '%s',
            '%d',
        )
    );

    return $new_entry;
}

/**
 * Add the top-level menu page.
 */
function awesome_reports_add_admin_menu() {
    add_menu_page(
        __('Reports', 'awesome-reports'),     // Page title
        __('Reports', 'awesome-reports'),     // Menu title
        'manage_options',                     // Capability
        'awesome_reports',                    // Menu slug
        'awesome_reports_stats_page',         // Function
        'dashicons-chart-bar',                // Icon
        2                                     // Position
    );
}
add_action('admin_menu', 'awesome_reports_add_admin_menu');

/**
 * Enqueue admin styles for the reports page.
 */
function awesome_reports_enqueue_admin_styles($hook) {
    // Only load on our reports page
    if ($hook !== 'toplevel_page_awesome_reports') {
        return;
    }

    wp_enqueue_style(
        'awesome-reports-admin',
        plugin_dir_url(__FILE__) . 'assets/css/admin.css',
        array(),
        AWESOME_REPORTS_VERSION
    );

    // Scripts for charts and interactivity
    wp_enqueue_script(
        'chart-js',
        'https://cdn.jsdelivr.net/npm/chart.js@4.4.3/dist/chart.umd.min.js',
        array(),
        '4.4.3',
        true
    );

    // Tooltipster for pill tooltips
    wp_enqueue_style(
        'tooltipster',
        'https://cdn.jsdelivr.net/npm/tooltipster@4.2.8/dist/css/tooltipster.bundle.min.css',
        array(),
        '4.2.8'
    );
    wp_enqueue_script(
        'tooltipster',
        'https://cdn.jsdelivr.net/npm/tooltipster@4.2.8/dist/js/tooltipster.bundle.min.js',
        array('jquery'),
        '4.2.8',
        true
    );

    wp_enqueue_script(
        'awesome-reports-admin',
        plugin_dir_url(__FILE__) . 'assets/js/admin.js',
        array('chart-js','tooltipster'),
        AWESOME_REPORTS_VERSION,
        true
    );
}
add_action('admin_enqueue_scripts', 'awesome_reports_enqueue_admin_styles');

/**
 * Main Awesome Reports page.
 */
function awesome_reports_stats_page() {
    global $wpdb;
    $awesome_reports_table_name = $wpdb->prefix . 'awesome_reports';

    // Get records from last 30 days
    $thirty_days_ago = date('Y-m-d', strtotime('-30 days'));

    $wp_updates = $wpdb->get_results($wpdb->prepare(
        "SELECT * FROM $awesome_reports_table_name WHERE type = 'wp' AND date >= %s AND version_before IS NOT NULL AND version_before != '' ORDER BY date DESC",
        $thirty_days_ago
    ));

    $theme_updates = $wpdb->get_results($wpdb->prepare(
        "SELECT * FROM $awesome_reports_table_name WHERE type = 'theme' AND date >= %s AND version_before IS NOT NULL AND version_before != '' ORDER BY date DESC",
        $thirty_days_ago
    ));

    $plugin_updates = $wpdb->get_results($wpdb->prepare(
        "SELECT * FROM $awesome_reports_table_name WHERE type = 'plugin' AND date >= %s AND version_before IS NOT NULL AND version_before != '' ORDER BY date DESC",
        $thirty_days_ago
    ));

    // Count updates for each category
    $wp_count = count($wp_updates);
    $theme_count = count($theme_updates);
    $plugin_count = count($plugin_updates);
    $server_count = 3; // Will be implemented later


?>

    <!-- First Toolbar: Logo and Branding -->
    <div class="ar-toolbar-primary">
        <a class="ar-logo" href="<?php echo admin_url('admin.php?page=awesome_reports'); ?>">
            <span class="ar-logo-icon">
                <svg xmlns="http://www.w3.org/2000/svg" width="512" height="512" viewBox="0 0 512 512" aria-hidden="true"><defs><linearGradient id="arLogoGrad" x1="0" y1="0" x2="0" y2="1"><stop offset="0%" stop-color="#4ade80"/><stop offset="100%" stop-color="#22c55e"/></linearGradient></defs><rect x="0" y="0" width="512" height="512" rx="72" fill="url(#arLogoGrad)"/><g fill="#fff"><rect width="54.89" height="157.89" x="144.01" y="238.11" rx="12.91"/><rect width="54.89" height="280" x="228.56" y="116" rx="17.19"/><rect width="54.89" height="218.94" x="313.1" y="177.06" rx="15.2"/></g></svg>
            </span>
            <h1 class="ar-logo-text"><?php _e('Awesome Reports', 'awesome-reports'); ?></h1>
        </a>
    </div>

    <!-- Second Toolbar: Time Filters -->
    <div class="ar-toolbar-secondary">
        <div class="ar-filters">
            <label for="ar-time-filter" class="screen-reader-text"><?php _e('Time Filter', 'awesome-reports'); ?></label>
            <select id="ar-time-filter" class="ar-time-filter">
                <option value="today"><?php _e('Today', 'awesome-reports'); ?></option>
                <option value="yesterday"><?php _e('Yesterday', 'awesome-reports'); ?></option>
                <option value="last-7-days"><?php _e('Last 7 Days', 'awesome-reports'); ?></option>
                <option value="last-14-days"><?php _e('Last 14 Days', 'awesome-reports'); ?></option>
                <option value="last-30-days" selected><?php _e('Last 30 Days', 'awesome-reports'); ?></option>
                <option value="last-month"><?php _e('Last Month', 'awesome-reports'); ?></option>
                <option value="this-month"><?php _e('This Month', 'awesome-reports'); ?></option>
                <option value="last-90-days"><?php _e('Last 90 Days', 'awesome-reports'); ?></option>
            </select>
        </div>
    </div>

    <div class="wrap">
        <div></div>
        <!-- Uptime Section -->
        <h2><?php _e('Uptime', 'awesome-reports'); ?></h2>
        <div class="ar-uptime-grid">
            <!-- Column stack: Status + Overview on one column -->
            <div class="ar-uptime-stack">
                <div class="ar-card ar-uptime-card ar-uptime-status">
                    <h3><?php _e('Uptime status', 'awesome-reports'); ?></h3>
                    <div class="ar-status-row">
                        <span class="ar-status-dot" aria-hidden="true"></span>
                        <span class="ar-status-text"><?php _e('Up', 'awesome-reports'); ?></span>
                    </div>
                    <div class="ar-subtle"><?php _e('Currently up for 13 days 21 hours 59 minutes', 'awesome-reports'); ?></div>
                </div>
                <div class="ar-card ar-uptime-card ar-uptime-overview">
                    <h3><?php _e('Uptime overview', 'awesome-reports'); ?></h3>
                    <div class="ar-pills-panel">
                        <div class="ar-pills-head">
                            <span class="ar-pills-title"><?php _e('Last 30 days', 'awesome-reports'); ?></span>
                            <span class="ar-pills-percent" id="ar-uptime-percent">100%</span>
                        </div>
                        <div id="ar-uptime-pills" class="ar-pills" aria-label="Daily uptime status"></div>
                        <div class="ar-pills-foot"><?php _e('2 incidents, 12 minutes 16 seconds down', 'awesome-reports'); ?></div>
                    </div>
                </div>
            </div>

            <!-- Card 2: Response time graph (span 2 columns) -->
            <div class="ar-card ar-uptime-card ar-uptime-response">
                <h3><?php _e('Response time', 'awesome-reports'); ?></h3>
                <div class="ar-chart-wrap">
                    <canvas id="ar-response-time-chart" aria-label="Response time" role="img"></canvas>
                </div>
            </div>

            <!-- Card 3: Incidents table on same row -->
            <div class="ar-card ar-uptime-card ar-uptime-incidents">
                <h3><?php _e('Incidents', 'awesome-reports'); ?></h3>
                <table class="ar-table">
                    <thead>
                        <tr>
                            <th><?php _e('Date', 'awesome-reports'); ?></th>
                            <th><?php _e('Duration', 'awesome-reports'); ?></th>
                            <th><?php _e('Description', 'awesome-reports'); ?></th>
                            <th><?php _e('Status', 'awesome-reports'); ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>Aug 2, 2025</td>
                            <td>5m</td>
                            <td>Brief outage</td>
                            <td><span class="ar-pill-status resolved"><?php _e('Resolved', 'awesome-reports'); ?></span></td>
                        </tr>
                        <tr>
                            <td>Aug 15, 2025</td>
                            <td>3m</td>
                            <td>API timeout</td>
                            <td><span class="ar-pill-status resolved"><?php _e('Resolved', 'awesome-reports'); ?></span></td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Software Updates Grid -->
        <h2><?php _e('Updates', 'awesome-reports'); ?></h2>
        <div class="ar-grid">
            <!-- WordPress Core Card -->
            <div class="ar-card ar-update-card">
                <h3><?php _e('WordPress updates', 'awesome-reports'); ?></h3>
                <div class="count">
                    <?php echo esc_html($wp_count); ?>
                </div>
                <?php if (!empty($wp_updates)) : ?>
                    <table class="ar-table">
                        <thead>
                            <tr>
                                <th><?php _e('Date', 'awesome-reports'); ?></th>
                                <th><?php _e('Name', 'awesome-reports'); ?></th>
                                <th><?php _e('Version', 'awesome-reports'); ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach (array_slice($wp_updates, 0, 4) as $update) : ?>
                                <tr>
                                    <td><?php echo esc_html(date('M j, Y', strtotime($update->date))); ?></td>
                                    <td><?php echo esc_html($update->name); ?></td>
                                    <td><?php echo esc_html($update->version_before); ?> → <?php echo esc_html($update->version_after); ?></td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                    <?php if (count($wp_updates) > 4) : ?>
                        <div class="update-more">
                            <?php printf(__('+ %d more updates', 'awesome-reports'), count($wp_updates) - 4); ?>
                        </div>
                    <?php endif; ?>
                <?php else : ?>
                    <div class="update-none">
                        <?php _e('No updates for this period', 'awesome-reports'); ?>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Themes Card -->
            <div class="ar-card ar-update-card">
                <h3><?php _e('Theme updates', 'awesome-reports'); ?></h3>
                <div class="count">
                    <?php echo esc_html($theme_count); ?>
                </div>
                <?php if (!empty($theme_updates)) : ?>
                    <table class="ar-table">
                        <thead>
                            <tr>
                                <th><?php _e('Date', 'awesome-reports'); ?></th>
                                <th><?php _e('Name', 'awesome-reports'); ?></th>
                                <th><?php _e('Version', 'awesome-reports'); ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach (array_slice($theme_updates, 0, 4) as $update) : ?>
                                <tr>
                                    <td><?php echo esc_html(date('M j, Y', strtotime($update->date))); ?></td>
                                    <td><?php echo esc_html($update->name); ?></td>
                                    <td><?php echo esc_html($update->version_before); ?> → <?php echo esc_html($update->version_after); ?></td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                    <?php if (count($theme_updates) > 4) : ?>
                        <div class="update-more">
                            <?php printf(__('+ %d more updates', 'awesome-reports'), count($theme_updates) - 4); ?>
                        </div>
                    <?php endif; ?>
                <?php else : ?>
                    <div class="update-none">
                        <?php _e('No updates for this period', 'awesome-reports'); ?>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Plugins Card -->
            <div class="ar-card ar-update-card">
                <h3><?php _e('Plugin updates', 'awesome-reports'); ?></h3>
                <div class="count">
                    <?php echo esc_html($plugin_count); ?>
                </div>
                <?php if (!empty($plugin_updates)) : ?>
                    <table class="ar-table">
                        <thead>
                            <tr>
                                <th><?php _e('Date', 'awesome-reports'); ?></th>
                                <th><?php _e('Name', 'awesome-reports'); ?></th>
                                <th><?php _e('Version', 'awesome-reports'); ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach (array_slice($plugin_updates, 0, 4) as $update) : ?>
                                <tr>
                                    <td><?php echo esc_html(date('M j, Y', strtotime($update->date))); ?></td>
                                    <td><?php echo esc_html($update->name); ?></td>
                                    <td><?php echo esc_html($update->version_before); ?> → <?php echo esc_html($update->version_after); ?></td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                    <?php if (count($plugin_updates) > 4) : ?>
                        <div class="update-more">
                            <?php printf(__('+ %d more updates', 'awesome-reports'), count($plugin_updates) - 4); ?>
                        </div>
                    <?php endif; ?>
                <?php else : ?>
                    <div class="update-none">
                        <?php _e('No updates for this period', 'awesome-reports'); ?>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Server Card -->
            <div class="ar-card ar-update-card">
                <h3><?php _e('Server updates', 'awesome-reports'); ?></h3>
                <div class="count"><?php echo esc_html($server_count); ?></div>
                <table class="ar-table">
                    <thead>
                        <tr>
                            <th><?php _e('Date', 'awesome-reports'); ?></th>
                            <th><?php _e('Name', 'awesome-reports'); ?></th>
                            <th><?php _e('Version', 'awesome-reports'); ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>Aug 1, 2025</td>
                            <td>PHP</td>
                            <td>8.1.12 → 8.1.13</td>
                        </tr>
                        <tr>
                            <td>Aug 4, 2025</td>
                            <td>Nginx</td>
                            <td>1.23.3 → 1.23.4</td>
                        </tr>
                        <tr>
                            <td>Aug 6, 2025</td>
                            <td>MySQL</td>
                            <td>8.0.31 → 8.0.32</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

    </div>
<?php
}