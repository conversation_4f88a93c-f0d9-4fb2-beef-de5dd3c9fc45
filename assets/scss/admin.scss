// Source SCSS for Awesome Reports admin styles
// This file is compiled to assets/css/admin.css via Gulp

/* Toolbar Styles */
.ar-toolbar-primary {
    background: #fff;
    padding: 10px 0;
    margin: 0 0 0 -20px;
    border-bottom: 1px solid #ddd;

    @media (max-width: 600px) {
        padding: 8px 15px;
        margin: -10px -20px 0 -20px;
    }
}

.ar-logo {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 0 20px;
    text-decoration: none;
}

.ar-logo:focus,
.ar-logo:hover,
.ar-logo:active,
.ar-logo:visited {
    text-decoration: none;
    outline: none;
    box-shadow: none;
}

.ar-logo-icon {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;

    @media (max-width: 600px) {
        width: 36px;
        height: 36px;
    }
}

.ar-logo-icon svg {
    width: 40px;
    height: 40px;

    @media (max-width: 600px) {
        width: 20px;
        height: 20px;
    }
}

.ar-logo-text {
    font-size: 18px;
    font-weight: 600;

    @media (max-width: 600px) {
        font-size: 16px;
    }
}

.ar-toolbar-secondary {
    background: #fff;
    border-bottom: 1px solid #ddd;
    padding: 12px 0;
    margin: 0 0 20px -20px;

    @media (max-width: 600px) {
        padding: 10px 15px;
        margin: 0 -20px 15px -20px;
    }
}

.ar-filters {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 0 20px;
}

.ar-time-filter {
    background: #fff;
    border: 1px solid #c3c4c7!important;
    border-radius: 4px;
    padding: 8px 12px;
    font-size: 14px;
    color: #1d2327;
    min-width: 160px;
    height: 32px;
    box-shadow: 0 1px 1px rgba(0,0,0,.04);
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;

    @media (max-width: 600px) {
        min-width: 140px;
        font-size: 13px;
        padding: 6px 10px;
        height: 30px;
    }
}

.ar-time-filter:focus {
    border-color: #2271b1;
    box-shadow: 0 0 0 1px #2271b1;
    outline: none;
}

.ar-time-filter:hover {
    border-color: #8c8f94;
}

.ar-table {
    width: 100% !important;
    max-width: none !important;
}

.ar-table th,
.ar-table td {
    padding: 12px 10px !important;
}

.ar-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 12px;
    margin-bottom: 30px;

    @media (max-width: 1200px) {
        grid-template-columns: repeat(2, 1fr);
    }

    @media (max-width: 600px) {
        grid-template-columns: 1fr;
    }
}

.ar-card {
    background: #fff;
    border: 1px solid #ddd;
    padding: 15px 15px 5px 15px;
    border-radius: 6px;
    box-shadow: 0px 1px 2px rgba(16, 24, 40, .1);

    @media (max-width: 600px) {
        .count {
            flex-direction: column;
            align-items: flex-start;
            gap: 5px;
        }
    }
}

.ar-card h3 {
    margin: 0 0 15px 0;
    font-size: 14px;
    font-weight: 600;
    color: #23282d;
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px 15px;
    margin: -15px -15px 15px -15px;
    border-bottom: 1px solid #e1e1e1;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
}

.ar-card .dashicons {
    width: 20px;
    height: 20px;
    font-size: 20px;
    color: #98a2b3;
}

.ar-card .count {
    font-size: 48px;
    font-weight: bold;
    color: #1d2327;
    margin: 10px 0 5px 0;
    line-height: 1;
    display: flex;
    align-items: baseline;
    gap: 10px;
}


.update-more,
.update-none {
    color: #646970;
    font-style: italic;
    padding: 10px 15px;
}


/* Uptime Grid + Cards */
.ar-uptime-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 12px;
    margin-bottom: 30px;
}
.ar-uptime-stack {
    display: flex;
    flex-direction: column;
    gap: 12px;
    height: 100%;
}
.ar-uptime-stack .ar-card {
    flex: 1;
}
.ar-uptime-response { grid-column: span 2; }
.ar-uptime-incidents { grid-column: span 1; }

.ar-uptime-overview .ar-overview-top { margin-bottom: 4px; }
.ar-overview-divider { height: 1px; background: #eef0f2; margin: 10px 0 12px; }
.ar-uptime-overview .ar-overview-bottom { display: flex; flex-direction: column; gap: 10px; }

.ar-card .ar-subtle {
    color: #646970;
    margin-top: 8px;
}

.ar-status-row {
    display: flex;
    align-items: center;
    gap: 12px;
    margin: 8px 0 12px;
}
.ar-status-dot {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: #22c55e;
    box-shadow: 0 0 0 4px rgba(34,197,94,0.2);
}
.ar-status-text {
    font-size: 24px;
    font-weight: 800;
}

.ar-badge {
    display: inline-block;
    padding: 4px 10px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
}
.ar-badge-up {
    background: #e8f5e8;
    color: #0b7a0b;
    border: 1px solid #c6e1c6;
}

.ar-big-number {
    font-size: 40px;
    font-weight: 800;
    line-height: 1;
    color: #1d2327;
    margin-bottom: 10px;
}

.ar-uptime-response {
    grid-column: span 2;
    overflow: hidden;
}
.ar-chart-wrap {
    height: 240px;
    overflow: visible;
    position: relative;
    z-index: 10;
}
.ar-chart-wrap canvas {
    width: 100% !important;
    height: 100% !important;
    max-width: 100%;
    position: relative;
    z-index: 10;
}

/* Pills */
.ar-pills-panel {
    background: transparent;
    border: none;
    padding: 0;
    margin-bottom: 15px;
}
.ar-pills-head {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 10px;
}
.ar-pills-title { font-weight: 600; font-size: 14px; color: #1e293b; }
.ar-pills-percent { font-weight: 700; font-size: 14px; color: #1e293b; }

.ar-pills {
    display: grid;
    grid-template-columns: repeat(30, 1fr);
    gap: 4px;
    padding: 0;
    margin-bottom: 8px;
}
.ar-pill {
    width: 100%;
    height: 32px;
    border-radius: 999px;
    background: #22c55e;
    border: none;
    transition: opacity 120ms ease;
}
.ar-table { width: 100%; border-collapse: collapse; table-layout: fixed; }
.ar-table thead th { text-align: left; font-weight: 600; font-size: 12px; color: #6b7280; padding: 8px 12px; border-bottom: 1px solid #e5e7eb; }
.ar-table tbody td { padding: 10px 12px; border-bottom: 1px solid #f3f4f6; font-size: 13px; color: #111827; }
.ar-table tbody tr:nth-child(odd) td { background: #fafafa; }

/* Update table column widths and styling */
.ar-card .ar-table th:nth-child(1),
.ar-card .ar-table td:nth-child(1) { width: 23%; }
.ar-card .ar-table th:nth-child(2),
.ar-card .ar-table td:nth-child(2) { width: 49%; }
.ar-card .ar-table th:nth-child(3),
.ar-card .ar-table td:nth-child(3) { width: 28%; text-align: right; }

/* Update table cell colors */
.ar-update-card .ar-table tbody td:nth-child(1) {
    color: #6b7280; /* Grey color for dates */
}
.ar-update-card .ar-table tbody td:nth-child(3) {
    color: #2271b1; /* Blue color for versions */
}

/* Incidents table column widths */
.ar-uptime-card .ar-table th:nth-child(1),
.ar-uptime-card .ar-table td:nth-child(1) { width: 25%; }
.ar-uptime-card .ar-table th:nth-child(2),
.ar-uptime-card .ar-table td:nth-child(2) { width: 15%; }
.ar-uptime-card .ar-table th:nth-child(3),
.ar-uptime-card .ar-table td:nth-child(3) { width: 40%; text-align: left; }
.ar-uptime-card .ar-table th:nth-child(4),
.ar-uptime-card .ar-table td:nth-child(4) { width: 20%; }
.ar-pill-status {
    display: inline-block;
    padding: 2px 8px;
    border-radius: 10px;
    font-size: 12px;
    line-height: 1.2;
    vertical-align: baseline;
}
.ar-pill-status.resolved { background: #e8f5e8; color: #0b7a0b; border: 1px solid #c6e1c6; }

.ar-pill:hover {
    opacity: 0.7;
}
.ar-pill.up { background: #22c55e; }
.ar-pill.down { background: #ef4444; }

.ar-pills-foot {
    color: #64748b;
}

/* light tooltipster theme */
.tooltipster-sidetip.tooltipster-light .tooltipster-box {
    background: #ffffff !important;
    color: #1e293b !important;
    border: 1px solid #e2e8f0 !important;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15) !important;
    border-radius: 6px !important;
}
.tooltipster-sidetip.tooltipster-light .tooltipster-content {
    padding: 8px 12px !important;
    font-size: 12px !important;
    line-height: 1.4 !important;
    color: #1e293b !important;
}
.tooltipster-sidetip.tooltipster-light .tooltipster-arrow-background {
    border-top-color: #ffffff !important;
}
.tooltipster-sidetip.tooltipster-light .tooltipster-arrow-border {
    border-top-color: #e2e8f0 !important;
}

@media (max-width: 1200px) {
    .ar-uptime-grid { grid-template-columns: repeat(2, 1fr); }
    .ar-uptime-response { grid-column: span 2; }
}

@media (max-width: 600px) {
    .ar-uptime-grid { grid-template-columns: 1fr; }
    .ar-uptime-response { grid-column: span 1; }
}


