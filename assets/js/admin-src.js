// Source file for admin.js - bundled via webpack in gulpfile.js
// Edit this file, then run `npm run build` to update assets/js/admin.js
import { createPopper } from '@popperjs/core';
import tippy from 'tippy.js';

(function(){
  // Build last 30 days labels deterministically
  const days = 30;
  const labels = [];
  const dateObjs = [];
  const now = new Date();
  for (let i = days - 1; i >= 0; i--) {
    const d = new Date(now);
    d.setHours(12,0,0,0);
    d.setDate(now.getDate() - i);
    labels.push(d.toLocaleDateString(undefined, { month: 'short', day: 'numeric' }));
    dateObjs.push(d);
  }

  // Response time chart (fixed sample data + gradient + improved tooltip and hover)
  const canvas = document.getElementById('ar-response-time-chart');
  if (canvas && window.Chart) {
    const ctx = canvas.getContext('2d');

    const sampleResponseMs = [
      320, 310, 305, 295, 290, 300, 315, 310, 305, 520,
      340, 330, 325, 315, 310, 300, 295, 290, 285, 280,
      275, 270, 265, 600, 320, 315, 310, 305, 300, 295
    ];

    const gradient = (() => {
      const g = ctx.createLinearGradient(0, 0, 0, canvas.height || 220);
      g.addColorStop(0, 'rgba(34,113,177,0.25)');
      g.addColorStop(1, 'rgba(34,113,177,0.00)');
      return g;
    })();

    const hoverLine = {
      id: 'hoverLine',
      afterDatasetsDraw(chart) {
        const active = chart.tooltip?.getActiveElements?.();
        if (!active || active.length === 0) return;
        const { ctx, chartArea: { top, bottom }, scales: { x } } = chart;
        const idx = active[0].index;
        const xPos = x.getPixelForValue(idx);
        ctx.save();
        ctx.beginPath();
        ctx.moveTo(xPos, top);
        ctx.lineTo(xPos, bottom);
        ctx.lineWidth = 1;
        ctx.strokeStyle = 'rgba(0,0,0,0.12)';
        ctx.setLineDash([4, 4]);
        ctx.stroke();
        ctx.setLineDash([]);
        ctx.restore();
      }
    };

    // eslint-disable-next-line no-undef
    new Chart(ctx, {
      type: 'line',
      data: {
        labels,
        datasets: [{
          label: 'Response time (ms)',
          data: sampleResponseMs,
          borderColor: 'rgba(34, 113, 177, 1)',
          backgroundColor: gradient,
          fill: true,
          tension: 0.25,
          pointRadius: 0,
          pointHoverRadius: 6,
          pointHoverBorderWidth: 3,
          pointHoverBackgroundColor: 'rgba(34,113,177,1)',
          pointHoverBorderColor: '#fff',
          borderWidth: 2,
          hoverBorderWidth: 3,
          clip: false
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        interaction: { mode: 'index', intersect: false },
        layout: {
          padding: {
            top: 15,
            right: 0,
            bottom: 15,
            left: 0
          }
        },
        scales: {
          y: {
            beginAtZero: true,
            ticks: { callback: (v) => v + 'ms' },
            grid: { color: 'rgba(0,0,0,0.05)' }
          },
          x: {
            grid: { display: false },
            ticks: {
              // ADDED: This forces Chart.js to obey our callback function
              // without trying to automatically skip labels to prevent overlap.
              autoSkip: false,

              // The callback is the correct way to control which labels are shown.
              // Your logic correctly shows the first, last, and every 7th label.
              callback: function(_, index) {
                if (index % 7 === 0 || index === labels.length - 1) {
                  return labels[index];
                }
                return ''; // Return an empty string for ticks we want to hide
              },
              maxRotation: 0,
              minRotation: 0,
            }
          }
        },
        plugins: {
          legend: { display: false },
          tooltip: {
            enabled: true,
            backgroundColor: '#ffffff',
            titleColor: '#111827',
            bodyColor: '#111827',
            borderColor: '#e5e7eb',
            borderWidth: 1,
            padding: 10,
            displayColors: false,
            callbacks: {
              title: (items) => items?.[0]?.label ?? '',
              label: (ctx) => `${ctx.parsed.y}ms`
            },
            titleFont: { size: 12, weight: 'normal' },
            bodyFont: { size: 14, weight: 'bold' }
          }
        }
      },
      plugins: [hoverLine]
    });
  }

  // Daily pills: deterministic 30-day data with tooltips
  const pills = document.getElementById('ar-uptime-pills');
  if (pills) {
    // Define fixed up/down days (0=up,1=down)
    const downIdx = new Set([9, 23]); // match the spikes roughly
    for (let i = 0; i < days; i++) {
      const statusDown = downIdx.has(i);
      const pill = document.createElement('div');
      pill.className = 'ar-pill ' + (statusDown ? 'down' : 'up');
      const label = labels[i];
      const tip = statusDown ? `${label}<br><strong style="font-size: 14px;">Down 5m 3s</strong>` : `${label}<br><strong style="font-size: 14px;">Up 100%</strong>`;
      pill.dataset.tooltip = tip;
      pill.setAttribute('aria-label', tip);
      pills.appendChild(pill);
    }

    // Initialize Tippy.js with Chart.js-like behavior
    tippy(pills.querySelectorAll('.ar-pill'), {
      content(reference) {
        return reference.getAttribute('data-tooltip');
      },
      allowHTML: true,
      theme: 'ar-light',
      followCursor: 'horizontal',
      hideOnClick: false,
      trigger: 'mouseenter',
      animation: 'fade',
      placement: 'top',
      offset: [0, 8],
      arrow: true,
      interactive: true,
      interactiveBorder: 2, // Large border to cover gaps between pills
      interactiveDebounce: 0,
      appendTo: () => document.body,
    });
  }

})();
